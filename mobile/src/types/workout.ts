// export interface WorkoutLog {
//     id: any;
//     date: string;
//     type: string;
//     duration: string;
//     trainingPlanId: any;
//     userId: any;
//     exercises: {
//       name: string;
//       sets: {
//         weight: number;
//         reps: number;
//       }[];
//     }[];
//   }

export interface WorkoutLoggerProps {
    userId: string;
    onWorkoutStart: () => void;
    onWorkoutEnd: () => void;
    onRestStart: (duration: number) => void;
    onPauseToggle: (paused: boolean) => void;
    restTimer: number;
    isPaused: boolean;
}

export interface ExerciseLog {
    name: string;
    sets: { weight: number; reps: number }[];
}

// export interface Exercise {
//     id: string;
//     name: string;
//     sets: string;
//     reps: string;
//     rest: string;
//     instructions?: string;
// }

// export interface Day {
//     id: string;
//     day: string;
//     focus: string;
//     exercises: Exercise[];
// }


export interface WorkoutSet {
  weight: number;
  reps: number;
  notes?: string;
  completed?: boolean;
}

export interface WorkoutExercise {
  id?: string;
  exercise_id?: string;
  name: string;
  sets: WorkoutSet[];
  category?: string;
  instructions?: string;
  targetReps?: string;
  currentSet?: number;
  rest?: string;
}

export interface WorkoutLog {
  id: string | null;
  date: string;
  type: string | null;
  duration: string;
  exercises: WorkoutExercise[];
  notes?: string;
  trainingPlanId?: string | null;
  userId?: string;
  trainingPlan?: {
    day: string,
    focus: string
  }
}


export interface Exercise {
  id: string;
  sets: string;
  reps: string;
  rest: string;
  instructions?: string;
  name: string;
}


export interface Day {
  id: string;
  day: string;
  focus: string;
  exercises: Exercise[];
}