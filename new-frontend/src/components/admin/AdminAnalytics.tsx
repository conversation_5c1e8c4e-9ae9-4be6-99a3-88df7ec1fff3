import React from "react";
import {
  TrendingUp,
  Users,
  Activity,
  Award,
  AlertTriangle,
} from "lucide-react";

export const AdminAnalytics: React.FC = () => {
  return (
    <div className="space-y-6">
      {/* Analytics Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-xl shadow-lg p-6 border-2 border-blue-200">
          <div className="flex items-center gap-3 mb-4">
            <div className="p-3 rounded-xl bg-blue-100">
              <Users className="h-6 w-6 text-blue-600" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-800">
                גידול משתמשים
              </h3>
              <p className="text-3xl font-bold text-blue-600">+{10}%</p>
            </div>
          </div>
          <div className="flex items-center gap-2 text-sm text-green-600">
            <TrendingUp className="h-4 w-4" />
            <span>{10} משתמשים בסך הכל</span>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-lg p-6 border-2 border-purple-200">
          <div className="flex items-center gap-3 mb-4">
            <div className="p-3 rounded-xl bg-purple-100">
              <Activity className="h-6 w-6 text-purple-600" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-800">מעורבות</h3>
              <p className="text-3xl font-bold text-purple-600">{10}%</p>
            </div>
          </div>
          <div className="flex items-center gap-2 text-sm text-green-600">
            <TrendingUp className="h-4 w-4" />
            <span>{10} משתמשים פעילים</span>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-lg p-6 border-2 border-emerald-200">
          <div className="flex items-center gap-3 mb-4">
            <div className="p-3 rounded-xl bg-emerald-100">
              <Award className="h-6 w-6 text-emerald-600" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-800">
                השגת מטרות
              </h3>
              <p className="text-3xl font-bold text-emerald-600">{10}%</p>
            </div>
          </div>
          <div className="flex items-center gap-2 text-sm text-green-600">
            <TrendingUp className="h-4 w-4" />
            <span>מעל היעד</span>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-lg p-6 border-2 border-red-200">
          <div className="flex items-center gap-3 mb-4">
            <div className="p-3 rounded-xl bg-red-100">
              <AlertTriangle className="h-6 w-6 text-red-600" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-800">
                שיעור נטישה
              </h3>
              <p className="text-3xl font-bold text-red-600">{10}%</p>
            </div>
          </div>
          <div className="flex items-center gap-2 text-sm text-green-600">
            <TrendingUp className="h-4 w-4" />
            <span>{10}% שמירה על משתמשים</span>
          </div>
        </div>
      </div>

      {/* Detailed Analytics */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Trainer Performance */}
        <div className="bg-white rounded-xl shadow-lg p-6 border-2 border-gray-200">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">
            ביצועי מאמנים
          </h3>
          {/* <div className="space-y-4">
            {analytics.trainerPerformance.map((trainer, index) => (
              <div key={index} className="bg-gray-50 p-4 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <span className="font-medium text-gray-700">
                    {trainer.name}
                  </span>
                  <span className="text-sm text-gray-500">
                    {trainer.trainees} trainees
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="flex-1 h-2 bg-gray-200 rounded-full overflow-hidden">
                    <div
                      className="h-full bg-blue-600 rounded-full"
                      style={{ width: `${trainer.success}%` }}
                    />
                  </div>
                  <span className="text-sm font-medium text-gray-600">
                    {trainer.success}%
                  </span>
                </div>
              </div>
            ))}
          </div> */}
        </div>

        {/* Goal Achievement by Category */}
        {/* <div className="bg-white rounded-xl shadow-lg p-6 border-2 border-gray-200">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">Goal Achievement by Category</h3>
          <div className="space-y-4">
            {analytics.categoryAchievement.map((category, index) => (
              <div key={index} className="bg-gray-50 p-4 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <span className="font-medium text-gray-700">{category.category}</span>
                  <span className="text-sm font-medium text-gray-600">{category.rate}%</span>
                </div>
                <div className="h-2 bg-gray-200 rounded-full overflow-hidden">
                  <div 
                    className="h-full bg-green-600 rounded-full"
                    style={{ width: `${category.rate}%` }}
                  />
                </div>
              </div>
            ))}
          </div>
        </div> */}
      </div>

      {/* User Activity Timeline */}
      <div className="bg-white rounded-xl shadow-lg p-6 border-2 border-gray-200">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">
          ציר זמן פעילות משתמשים
        </h3>

        {/* <div className="space-y-4">
          {analytics.userActivity.map((activity, index) => (
            <div key={index} className="flex items-center gap-4">
              <div className="w-24 text-sm text-gray-500">{activity.time}</div>
              <div className="flex-1">
                <div className="h-2 bg-gray-200 rounded-full overflow-hidden">
                  <div 
                    className="h-full bg-blue-600 rounded-full"
                    style={{ width: `${(activity.count / 400) * 100}%` }}
                  />
                </div>
              </div>
              <div className="w-48 text-right">
                <div className="text-sm font-medium text-gray-900">{activity.count}</div>
                <div className="text-xs text-gray-500">{activity.type}</div>
              </div>
            </div>
          ))}
        </div> */}
      </div>
    </div>
  );
};
