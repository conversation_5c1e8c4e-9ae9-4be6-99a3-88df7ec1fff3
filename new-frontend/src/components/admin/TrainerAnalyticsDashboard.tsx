import React from "react";
import {
  ChevronLeft,
  Users,
  Target,
  Clock,
  Award,
  CheckCircle,
  XCircle,
} from "lucide-react";
import { Trainer } from "../../data/trainers";

interface TrainerAnalyticsDashboardProps {
  trainer: Trainer;
  onBack: () => void;
}

export const TrainerAnalyticsDashboard: React.FC<
  TrainerAnalyticsDashboardProps
> = ({ trainer, onBack }) => {
  if (!trainer) {
    return <div>מאמן לא נמצא</div>;
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between bg-white rounded-xl p-6 shadow-lg border-2 border-blue-200">
        <div className="flex items-center gap-4">
          <button
            onClick={onBack}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors duration-200"
          >
            <ChevronLeft className="h-6 w-6 text-gray-600" />
          </button>
          <div>
            <h1 className="text-2xl font-bold text-gray-800">{trainer.name}</h1>
            <p className="text-gray-600">
              {trainer.role === "senior_trainer"
                ? "מאמן בכיר"
                : trainer.role === "junior_trainer"
                ? "מאמן צעיר"
                : "מומחה תזונה"}
            </p>
          </div>
        </div>
        <div className="flex items-center gap-3">
          <span
            className={`px-3 py-1 rounded-full text-sm font-medium ${
              trainer.status === "active"
                ? "bg-green-100 text-green-800"
                : trainer.status === "inactive"
                ? "bg-red-100 text-red-800"
                : "bg-yellow-100 text-yellow-800"
            }`}
          >
            {trainer.status.charAt(0).toUpperCase() + trainer.status.slice(1)}
          </span>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-xl p-6 shadow-lg border-2 border-blue-200">
          <div className="flex items-center gap-3 mb-2">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Users className="h-5 w-5 text-blue-600" />
            </div>
            <h3 className="text-sm font-medium text-gray-600">לקוחות פעילים</h3>
          </div>
          <div className="mt-2">
            <div className="text-2xl font-bold text-gray-900">{20}</div>
            <div className="text-sm text-gray-500">מתוך {80} סך הכל</div>
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 shadow-lg border-2 border-purple-200">
          <div className="flex items-center gap-3 mb-2">
            <div className="p-2 bg-purple-100 rounded-lg">
              <Target className="h-5 w-5 text-purple-600" />
            </div>
            <h3 className="text-sm font-medium text-gray-600">
              התקדמות הלקוחות
            </h3>
          </div>
          <div className="mt-2">
            <div className="text-2xl font-bold text-gray-900">
              {Math.round(25)}%
            </div>
            <div className="text-sm text-gray-500">שיעור התקדמות ממוצע</div>
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 shadow-lg border-2 border-emerald-200">
          <div className="flex items-center gap-3 mb-2">
            <div className="p-2 bg-emerald-100 rounded-lg">
              <Clock className="h-5 w-5 text-emerald-600" />
            </div>
            <h3 className="text-sm font-medium text-gray-600">שימור לקוחות</h3>
          </div>
          <div className="mt-2">
            <div className="text-2xl font-bold text-gray-900">
              {Math.round(20)}%
            </div>
            <div className="text-sm text-gray-500">שיעור שימור</div>
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 shadow-lg border-2 border-yellow-200">
          <div className="flex items-center gap-3 mb-2">
            <div className="p-2 bg-yellow-100 rounded-lg">
              <Award className="h-5 w-5 text-yellow-600" />
            </div>
            <h3 className="text-sm font-medium text-gray-600">מטרות שהושגו</h3>
          </div>
          <div className="mt-2">
            <div className="text-2xl font-bold text-gray-900">{10}</div>
            <div className="text-sm text-gray-500">השבוע</div>
          </div>
        </div>
      </div>

      {/* Weekly Performance */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-white rounded-xl p-6 shadow-lg border-2 border-gray-200">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">
            סקירה שבועית
          </h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
              <div className="flex items-center gap-3">
                <Users className="h-5 w-5 text-green-600" />
                <span className="font-medium text-green-900">
                  לקוחות פעילים
                </span>
              </div>
              <span className="text-lg font-semibold text-green-900">{5}</span>
            </div>

            <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
              <div className="flex items-center gap-3">
                <CheckCircle className="h-5 w-5 text-blue-600" />
                <span className="font-medium text-blue-900">
                  אימונים שהושלמו
                </span>
              </div>
              <span className="text-lg font-semibold text-blue-900">{11}</span>
            </div>

            <div className="flex items-center justify-between p-3 bg-red-50 rounded-lg">
              <div className="flex items-center gap-3">
                <XCircle className="h-5 w-5 text-red-600" />
                <span className="font-medium text-red-900">
                  אימונים שהוחמצו
                </span>
              </div>
              <span className="text-lg font-semibold text-red-900">{15}</span>
            </div>

            <div className="flex items-center justify-between p-3 bg-purple-50 rounded-lg">
              <div className="flex items-center gap-3">
                <Target className="h-5 w-5 text-purple-600" />
                <span className="font-medium text-purple-900">
                  מטרות שהושגו
                </span>
              </div>
              <span className="text-lg font-semibold text-purple-900">
                {12}
              </span>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 shadow-lg border-2 border-gray-200">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">
            סקירת התקדמות לקוחות
          </h3>
          <div className="space-y-4">
            {/* {analytics.clientProgress.map((client, index) => (
              <div key={index} className="bg-gray-50 p-4 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-3">
                    <div
                      className={`p-2 rounded-lg ${
                        client.status === "ahead"
                          ? "bg-green-100"
                          : client.status === "on_track"
                          ? "bg-blue-100"
                          : "bg-yellow-100"
                      }`}
                    >
                      {client.status === "ahead" ? (
                        <TrendingUp className="h-4 w-4 text-green-600" />
                      ) : client.status === "on_track" ? (
                        <Target className="h-4 w-4 text-blue-600" />
                      ) : (
                        <AlertTriangle className="h-4 w-4 text-yellow-600" />
                      )}
                    </div>
                    <div>
                      <div className="font-medium text-gray-900">
                        {client.name}
                      </div>
                      <div className="text-sm text-gray-500">
                        {client.goal === "fat_loss"
                          ? "Fat Loss"
                          : "Muscle Gain"}
                      </div>
                    </div>
                  </div>
                  <span className="text-sm font-medium text-gray-900">
                    {Math.round(client.progress)}%
                  </span>
                </div>
                <div className="h-2 bg-gray-200 rounded-full overflow-hidden">
                  <div
                    className={`h-full rounded-full ${
                      client.status === "ahead"
                        ? "bg-green-600"
                        : client.status === "on_track"
                        ? "bg-blue-600"
                        : "bg-yellow-600"
                    }`}
                    style={{ width: `${client.progress}%` }}
                  />
                </div>
              </div>
            ))} */}
          </div>
        </div>
      </div>

      {/* Performance Trends */}
      <div className="bg-white rounded-xl p-6 shadow-lg border-2 border-gray-200">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">
          מגמות ביצועים
        </h3>
        <div className="grid grid-cols-2 gap-6">
          <div>
            <h4 className="text-sm font-medium text-gray-600 mb-2">
              שיעור שימור לקוחות
            </h4>
            <div className="h-40 flex items-end gap-2">
              {/* {analytics.performanceMetrics.clientRetention.map(
                (rate, index) => (
                  <div
                    key={index}
                    className="flex-1 bg-blue-600 rounded-t-lg hover:bg-blue-700 transition-colors duration-200"
                    style={{ height: `${rate}%` }}
                  >
                    <div className="text-xs text-white text-center mt-2 transform -rotate-90">
                      {Math.round(rate)}%
                    </div>
                  </div>
                )
              )} */}
            </div>
            <div className="flex justify-between mt-2 text-xs text-gray-500">
              <span>יום ב'</span>
              <span>יום ג'</span>
              <span>יום ד'</span>
              <span>יום ה'</span>
              <span>יום ו'</span>
              <span>שבת</span>
              <span>יום א'</span>
            </div>
          </div>

          <div>
            <h4 className="text-sm font-medium text-gray-600 mb-2">
              שיעור התקדמות לקוחות
            </h4>
            <div className="h-40 flex items-end gap-2">
              {/* {analytics.performanceMetrics.clientProgress.map(
                (rate, index) => (
                  <div
                    key={index}
                    className="flex-1 bg-green-600 rounded-t-lg hover:bg-green-700 transition-colors duration-200"
                    style={{ height: `${rate}%` }}
                  >
                    <div className="text-xs text-white text-center mt-2 transform -rotate-90">
                      {Math.round(rate)}%
                    </div>
                  </div>
                )
              )} */}
            </div>
            <div className="flex justify-between mt-2 text-xs text-gray-500">
              <span>יום ב'</span>
              <span>יום ג'</span>
              <span>יום ד'</span>
              <span>יום ה'</span>
              <span>יום ו'</span>
              <span>שבת</span>
              <span>יום א'</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
